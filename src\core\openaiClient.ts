import axios, { AxiosInstance, AxiosError } from 'axios';
import { AIAgentConfiguration } from './configManager';

export interface OpenAIMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface OpenAIResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

export interface OpenAIError {
    error: {
        message: string;
        type: string;
        param?: string;
        code?: string;
    };
}

export class OpenAIClient {
    private client: AxiosInstance;
    private config: AIAgentConfiguration;
    private retryDelays = [1000, 2000, 4000]; // Exponential backoff

    constructor(config: AIAgentConfiguration) {
        this.config = config;
        this.client = axios.create({
            baseURL: config.openaiBaseUrl,
            timeout: config.openaiTimeout,
            headers: {
                'Authorization': `Bearer ${config.openaiApiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'HEAVENLYDEMON-AI-Agent/1.0.0'
            }
        });

        // Add request interceptor for logging
        this.client.interceptors.request.use(
            (config) => {
                if (this.config.debug) {
                    console.log('OpenAI Request:', {
                        url: config.url,
                        method: config.method,
                        headers: { ...config.headers, Authorization: '[REDACTED]' }
                    });
                }
                return config;
            },
            (error) => {
                console.error('OpenAI Request Error:', error);
                return Promise.reject(error);
            }
        );

        // Add response interceptor for logging
        this.client.interceptors.response.use(
            (response) => {
                if (this.config.debug) {
                    console.log('OpenAI Response:', {
                        status: response.status,
                        usage: response.data.usage
                    });
                }
                return response;
            },
            (error) => {
                console.error('OpenAI Response Error:', this.formatError(error));
                return Promise.reject(error);
            }
        );
    }

    public async createChatCompletion(
        messages: OpenAIMessage[],
        options: {
            model?: string;
            maxTokens?: number;
            temperature?: number;
            stream?: boolean;
            functions?: any[];
            functionCall?: any;
        } = {}
    ): Promise<OpenAIResponse> {
        const requestData = {
            model: options.model || this.config.openaiModel,
            messages,
            max_tokens: options.maxTokens || this.config.openaiMaxTokens,
            temperature: options.temperature ?? this.config.openaiTemperature,
            stream: options.stream || false,
            ...(options.functions && { functions: options.functions }),
            ...(options.functionCall && { function_call: options.functionCall })
        };

        return this.executeWithRetry(async () => {
            const response = await this.client.post('/chat/completions', requestData);
            return response.data;
        });
    }

    public async createCompletion(
        prompt: string,
        options: {
            model?: string;
            maxTokens?: number;
            temperature?: number;
            stop?: string[];
        } = {}
    ): Promise<any> {
        const requestData = {
            model: options.model || 'text-davinci-003',
            prompt,
            max_tokens: options.maxTokens || this.config.openaiMaxTokens,
            temperature: options.temperature ?? this.config.openaiTemperature,
            stop: options.stop
        };

        return this.executeWithRetry(async () => {
            const response = await this.client.post('/completions', requestData);
            return response.data;
        });
    }

    public async createEmbedding(
        input: string | string[],
        model: string = 'text-embedding-ada-002'
    ): Promise<any> {
        const requestData = {
            model,
            input
        };

        return this.executeWithRetry(async () => {
            const response = await this.client.post('/embeddings', requestData);
            return response.data;
        });
    }

    public async validateApiKey(): Promise<{ valid: boolean; error?: string }> {
        try {
            await this.client.get('/models');
            return { valid: true };
        } catch (error) {
            const axiosError = error as AxiosError<OpenAIError>;
            if (axiosError.response?.status === 401) {
                return { 
                    valid: false, 
                    error: 'Invalid API key. Please check your OpenAI API key.' 
                };
            }
            return { 
                valid: false, 
                error: `API validation failed: ${this.formatError(axiosError)}` 
            };
        }
    }

    public async getModels(): Promise<any> {
        try {
            const response = await this.client.get('/models');
            return response.data;
        } catch (error) {
            throw new Error(`Failed to fetch models: ${this.formatError(error as AxiosError<OpenAIError>)}`);
        }
    }

    private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
        let lastError: Error;

        for (let attempt = 0; attempt <= this.config.openaiMaxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                const axiosError = error as AxiosError<OpenAIError>;

                // Don't retry on certain errors
                if (this.shouldNotRetry(axiosError)) {
                    throw error;
                }

                // If this is the last attempt, throw the error
                if (attempt === this.config.openaiMaxRetries) {
                    throw error;
                }

                // Wait before retrying
                const delay = this.retryDelays[Math.min(attempt, this.retryDelays.length - 1)];
                await this.sleep(delay);

                if (this.config.debug) {
                    console.log(`Retrying OpenAI request (attempt ${attempt + 2}/${this.config.openaiMaxRetries + 1}) after ${delay}ms`);
                }
            }
        }

        throw lastError!;
    }

    private shouldNotRetry(error: AxiosError<OpenAIError>): boolean {
        if (!error.response) {
            return false; // Network errors should be retried
        }

        const status = error.response.status;
        
        // Don't retry on client errors (except rate limiting)
        if (status >= 400 && status < 500 && status !== 429) {
            return true;
        }

        // Don't retry on authentication errors
        if (status === 401 || status === 403) {
            return true;
        }

        return false;
    }

    private formatError(error: AxiosError<OpenAIError>): string {
        if (error.response?.data?.error) {
            const openAIError = error.response.data.error;
            return `${openAIError.type || 'OpenAI Error'}: ${openAIError.message}`;
        }

        if (error.response) {
            return `HTTP ${error.response.status}: ${error.response.statusText}`;
        }

        if (error.code === 'ECONNABORTED') {
            return 'Request timeout - OpenAI API did not respond in time';
        }

        if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            return 'Network error - Unable to connect to OpenAI API';
        }

        return error.message || 'Unknown error occurred';
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public updateConfig(config: AIAgentConfiguration): void {
        this.config = config;
        
        // Update client headers
        this.client.defaults.headers['Authorization'] = `Bearer ${config.openaiApiKey}`;
        this.client.defaults.baseURL = config.openaiBaseUrl;
        this.client.defaults.timeout = config.openaiTimeout;
    }

    public async estimateTokens(text: string): Promise<number> {
        // Rough estimation: 1 token ≈ 4 characters for English text
        // This is a simplified estimation - for production, consider using tiktoken
        return Math.ceil(text.length / 4);
    }

    public async checkRateLimit(): Promise<{
        requestsRemaining?: number;
        tokensRemaining?: number;
        resetTime?: Date;
    }> {
        try {
            // Make a minimal request to check rate limit headers
            const response = await this.client.get('/models', {
                params: { limit: 1 }
            });

            const headers = response.headers;
            return {
                requestsRemaining: headers['x-ratelimit-remaining-requests'] ? 
                    parseInt(headers['x-ratelimit-remaining-requests']) : undefined,
                tokensRemaining: headers['x-ratelimit-remaining-tokens'] ? 
                    parseInt(headers['x-ratelimit-remaining-tokens']) : undefined,
                resetTime: headers['x-ratelimit-reset-requests'] ? 
                    new Date(headers['x-ratelimit-reset-requests']) : undefined
            };
        } catch (error) {
            console.warn('Failed to check rate limit:', error);
            return {};
        }
    }
}
