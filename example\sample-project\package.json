{"name": "sample-express-api", "version": "1.0.0", "description": "Sample Express.js API for testing AI Agent", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["express", "api", "nodejs", "sample"], "author": "HEAVENLYDEMON AI Agent", "license": "MIT", "dependencies": {"express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}