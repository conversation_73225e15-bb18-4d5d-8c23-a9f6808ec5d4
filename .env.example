# HEAVENLYDEMON AI Agent Environment Configuration

# OpenAI API Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# API Configuration
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=60000
OPENAI_MAX_RETRIES=3

# Agent Configuration
AI_AGENT_MAX_CONTEXT_SIZE=100000
AI_AGENT_MAX_FILE_SIZE=1000000
AI_AGENT_BACKUP_RETENTION_DAYS=30
AI_AGENT_LOG_LEVEL=info

# Security Settings
AI_AGENT_ALLOW_DANGEROUS_COMMANDS=false
AI_AGENT_REQUIRE_CONFIRMATION=true
AI_AGENT_SANDBOX_MODE=true

# Performance Settings
AI_AGENT_INDEX_BATCH_SIZE=50
AI_AGENT_CONCURRENT_OPERATIONS=5
AI_AGENT_CACHE_TTL=3600

# Development Settings (for extension development)
AI_AGENT_DEBUG=false
AI_AGENT_VERBOSE_LOGGING=false
AI_AGENT_TELEMETRY_ENABLED=false
