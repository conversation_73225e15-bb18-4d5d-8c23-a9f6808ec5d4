import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface AIAgentConfiguration {
    // OpenAI Settings
    openaiApiKey: string;
    openaiModel: string;
    openaiMaxTokens: number;
    openaiTemperature: number;
    openaiBaseUrl: string;
    openaiTimeout: number;
    openaiMaxRetries: number;

    // Agent Settings
    maxContextSize: number;
    maxFileSize: number;
    backupRetentionDays: number;
    logLevel: 'debug' | 'info' | 'warn' | 'error';

    // Security Settings
    allowDangerousCommands: boolean;
    requireConfirmation: boolean;
    sandboxMode: boolean;

    // Performance Settings
    indexBatchSize: number;
    concurrentOperations: number;
    cacheTtl: number;

    // Development Settings
    debug: boolean;
    verboseLogging: boolean;
    telemetryEnabled: boolean;
}

export class ConfigManager {
    private static instance: ConfigManager;
    private config: AIAgentConfiguration;
    private context: vscode.ExtensionContext;

    private constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.config = this.loadConfiguration();
    }

    public static getInstance(context?: vscode.ExtensionContext): ConfigManager {
        if (!ConfigManager.instance) {
            if (!context) {
                throw new Error('ConfigManager requires context for initialization');
            }
            ConfigManager.instance = new ConfigManager(context);
        }
        return ConfigManager.instance;
    }

    private loadConfiguration(): AIAgentConfiguration {
        // Load from VS Code settings
        const vsCodeConfig = vscode.workspace.getConfiguration('aiAgent');
        
        // Load from environment variables
        const envConfig = this.loadEnvironmentConfig();
        
        // Load from .env file if exists
        const envFileConfig = this.loadEnvFile();

        // Merge configurations (priority: VS Code > Environment > .env file > defaults)
        return {
            // OpenAI Settings
            openaiApiKey: this.getConfigValue('openaiApiKey', vsCodeConfig, envConfig, envFileConfig) || '',
            openaiModel: this.getConfigValue('openaiModel', vsCodeConfig, envConfig, envFileConfig) || 'gpt-4',
            openaiMaxTokens: this.getConfigValue('openaiMaxTokens', vsCodeConfig, envConfig, envFileConfig) || 4000,
            openaiTemperature: this.getConfigValue('openaiTemperature', vsCodeConfig, envConfig, envFileConfig) || 0.7,
            openaiBaseUrl: this.getConfigValue('openaiBaseUrl', vsCodeConfig, envConfig, envFileConfig) || 'https://api.openai.com/v1',
            openaiTimeout: this.getConfigValue('openaiTimeout', vsCodeConfig, envConfig, envFileConfig) || 60000,
            openaiMaxRetries: this.getConfigValue('openaiMaxRetries', vsCodeConfig, envConfig, envFileConfig) || 3,

            // Agent Settings
            maxContextSize: this.getConfigValue('maxContextSize', vsCodeConfig, envConfig, envFileConfig) || 100000,
            maxFileSize: this.getConfigValue('maxFileSize', vsCodeConfig, envConfig, envFileConfig) || 1000000,
            backupRetentionDays: this.getConfigValue('backupRetentionDays', vsCodeConfig, envConfig, envFileConfig) || 30,
            logLevel: this.getConfigValue('logLevel', vsCodeConfig, envConfig, envFileConfig) || 'info',

            // Security Settings
            allowDangerousCommands: this.getConfigValue('allowDangerousCommands', vsCodeConfig, envConfig, envFileConfig) || false,
            requireConfirmation: this.getConfigValue('requireConfirmation', vsCodeConfig, envConfig, envFileConfig) !== false,
            sandboxMode: this.getConfigValue('sandboxMode', vsCodeConfig, envConfig, envFileConfig) !== false,

            // Performance Settings
            indexBatchSize: this.getConfigValue('indexBatchSize', vsCodeConfig, envConfig, envFileConfig) || 50,
            concurrentOperations: this.getConfigValue('concurrentOperations', vsCodeConfig, envConfig, envFileConfig) || 5,
            cacheTtl: this.getConfigValue('cacheTtl', vsCodeConfig, envConfig, envFileConfig) || 3600,

            // Development Settings
            debug: this.getConfigValue('debug', vsCodeConfig, envConfig, envFileConfig) || false,
            verboseLogging: this.getConfigValue('verboseLogging', vsCodeConfig, envConfig, envFileConfig) || false,
            telemetryEnabled: this.getConfigValue('telemetryEnabled', vsCodeConfig, envConfig, envFileConfig) || false
        };
    }

    private loadEnvironmentConfig(): Record<string, any> {
        return {
            openaiApiKey: process.env.OPENAI_API_KEY,
            openaiModel: process.env.OPENAI_MODEL,
            openaiMaxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,
            openaiTemperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined,
            openaiBaseUrl: process.env.OPENAI_BASE_URL,
            openaiTimeout: process.env.OPENAI_TIMEOUT ? parseInt(process.env.OPENAI_TIMEOUT) : undefined,
            openaiMaxRetries: process.env.OPENAI_MAX_RETRIES ? parseInt(process.env.OPENAI_MAX_RETRIES) : undefined,
            maxContextSize: process.env.AI_AGENT_MAX_CONTEXT_SIZE ? parseInt(process.env.AI_AGENT_MAX_CONTEXT_SIZE) : undefined,
            maxFileSize: process.env.AI_AGENT_MAX_FILE_SIZE ? parseInt(process.env.AI_AGENT_MAX_FILE_SIZE) : undefined,
            backupRetentionDays: process.env.AI_AGENT_BACKUP_RETENTION_DAYS ? parseInt(process.env.AI_AGENT_BACKUP_RETENTION_DAYS) : undefined,
            logLevel: process.env.AI_AGENT_LOG_LEVEL,
            allowDangerousCommands: process.env.AI_AGENT_ALLOW_DANGEROUS_COMMANDS === 'true',
            requireConfirmation: process.env.AI_AGENT_REQUIRE_CONFIRMATION !== 'false',
            sandboxMode: process.env.AI_AGENT_SANDBOX_MODE !== 'false',
            indexBatchSize: process.env.AI_AGENT_INDEX_BATCH_SIZE ? parseInt(process.env.AI_AGENT_INDEX_BATCH_SIZE) : undefined,
            concurrentOperations: process.env.AI_AGENT_CONCURRENT_OPERATIONS ? parseInt(process.env.AI_AGENT_CONCURRENT_OPERATIONS) : undefined,
            cacheTtl: process.env.AI_AGENT_CACHE_TTL ? parseInt(process.env.AI_AGENT_CACHE_TTL) : undefined,
            debug: process.env.AI_AGENT_DEBUG === 'true',
            verboseLogging: process.env.AI_AGENT_VERBOSE_LOGGING === 'true',
            telemetryEnabled: process.env.AI_AGENT_TELEMETRY_ENABLED === 'true'
        };
    }

    private loadEnvFile(): Record<string, any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) return {};

        const envPath = path.join(workspaceFolder.uri.fsPath, '.env');
        if (!fs.existsSync(envPath)) return {};

        try {
            const envContent = fs.readFileSync(envPath, 'utf-8');
            const envVars: Record<string, any> = {};

            envContent.split('\n').forEach(line => {
                const trimmed = line.trim();
                if (trimmed && !trimmed.startsWith('#')) {
                    const [key, ...valueParts] = trimmed.split('=');
                    if (key && valueParts.length > 0) {
                        const value = valueParts.join('=').trim();
                        envVars[this.envKeyToConfigKey(key.trim())] = this.parseEnvValue(value);
                    }
                }
            });

            return envVars;
        } catch (error) {
            console.warn('Failed to load .env file:', error);
            return {};
        }
    }

    private envKeyToConfigKey(envKey: string): string {
        const mapping: Record<string, string> = {
            'OPENAI_API_KEY': 'openaiApiKey',
            'OPENAI_MODEL': 'openaiModel',
            'OPENAI_MAX_TOKENS': 'openaiMaxTokens',
            'OPENAI_TEMPERATURE': 'openaiTemperature',
            'OPENAI_BASE_URL': 'openaiBaseUrl',
            'OPENAI_TIMEOUT': 'openaiTimeout',
            'OPENAI_MAX_RETRIES': 'openaiMaxRetries',
            'AI_AGENT_MAX_CONTEXT_SIZE': 'maxContextSize',
            'AI_AGENT_MAX_FILE_SIZE': 'maxFileSize',
            'AI_AGENT_BACKUP_RETENTION_DAYS': 'backupRetentionDays',
            'AI_AGENT_LOG_LEVEL': 'logLevel',
            'AI_AGENT_ALLOW_DANGEROUS_COMMANDS': 'allowDangerousCommands',
            'AI_AGENT_REQUIRE_CONFIRMATION': 'requireConfirmation',
            'AI_AGENT_SANDBOX_MODE': 'sandboxMode',
            'AI_AGENT_INDEX_BATCH_SIZE': 'indexBatchSize',
            'AI_AGENT_CONCURRENT_OPERATIONS': 'concurrentOperations',
            'AI_AGENT_CACHE_TTL': 'cacheTtl',
            'AI_AGENT_DEBUG': 'debug',
            'AI_AGENT_VERBOSE_LOGGING': 'verboseLogging',
            'AI_AGENT_TELEMETRY_ENABLED': 'telemetryEnabled'
        };
        return mapping[envKey] || envKey;
    }

    private parseEnvValue(value: string): any {
        // Remove quotes if present
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
        }

        // Parse boolean values
        if (value.toLowerCase() === 'true') return true;
        if (value.toLowerCase() === 'false') return false;

        // Parse numeric values
        if (/^\d+$/.test(value)) return parseInt(value);
        if (/^\d+\.\d+$/.test(value)) return parseFloat(value);

        return value;
    }

    private getConfigValue(key: string, ...sources: Record<string, any>[]): any {
        for (const source of sources) {
            if (source && source[key] !== undefined && source[key] !== null) {
                return source[key];
            }
        }
        return undefined;
    }

    public getConfig(): AIAgentConfiguration {
        return { ...this.config };
    }

    public async updateConfig(updates: Partial<AIAgentConfiguration>): Promise<void> {
        this.config = { ...this.config, ...updates };
        
        // Update VS Code settings for persistent storage
        const vsCodeConfig = vscode.workspace.getConfiguration('aiAgent');
        for (const [key, value] of Object.entries(updates)) {
            await vsCodeConfig.update(key, value, vscode.ConfigurationTarget.Workspace);
        }
    }

    public async validateConfiguration(): Promise<{ valid: boolean; errors: string[] }> {
        const errors: string[] = [];

        // Validate required settings
        if (!this.config.openaiApiKey) {
            errors.push('OpenAI API key is required. Set it in VS Code settings or environment variables.');
        }

        // Validate API key format
        if (this.config.openaiApiKey && !this.config.openaiApiKey.startsWith('sk-')) {
            errors.push('OpenAI API key appears to be invalid. It should start with "sk-".');
        }

        // Validate numeric ranges
        if (this.config.openaiMaxTokens < 1 || this.config.openaiMaxTokens > 32000) {
            errors.push('OpenAI max tokens must be between 1 and 32000.');
        }

        if (this.config.openaiTemperature < 0 || this.config.openaiTemperature > 2) {
            errors.push('OpenAI temperature must be between 0 and 2.');
        }

        // Validate URLs
        try {
            new URL(this.config.openaiBaseUrl);
        } catch {
            errors.push('OpenAI base URL is not a valid URL.');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    public async promptForApiKey(): Promise<string | undefined> {
        const apiKey = await vscode.window.showInputBox({
            prompt: 'Enter your OpenAI API Key',
            password: true,
            placeHolder: 'sk-...',
            validateInput: (value) => {
                if (!value) return 'API key is required';
                if (!value.startsWith('sk-')) return 'API key should start with "sk-"';
                return null;
            }
        });

        if (apiKey) {
            await this.updateConfig({ openaiApiKey: apiKey });
            return apiKey;
        }

        return undefined;
    }

    public onConfigurationChanged(callback: () => void): vscode.Disposable {
        return vscode.workspace.onDidChangeConfiguration((event) => {
            if (event.affectsConfiguration('aiAgent')) {
                this.config = this.loadConfiguration();
                callback();
            }
        });
    }
}
