{"name": "heavenly<PERSON><PERSON>", "displayName": "HEAVENLYDEMON AI Agent", "description": "A comprehensive AI coding assistant that understands your project, writes code, executes commands, and maintains persistent memory", "version": "0.0.1", "publisher": "heavenly<PERSON><PERSON>", "engines": {"vscode": "^1.102.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "assistant", "coding", "gpt", "automation"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "heavenlydemon.openAIPanel", "title": "Open AI Agent Panel", "category": "AI Agent"}, {"command": "heavenlydemon.revertChanges", "title": "Revert All <PERSON>", "category": "AI Agent"}, {"command": "heavenlydemon.clearMemory", "title": "Clear Agent Memory", "category": "AI Agent"}], "views": {"aiAgent": [{"id": "aiAgentPanel", "name": "AI Agent", "type": "webview", "when": "workspaceHasFolder"}]}, "viewsContainers": {"activitybar": [{"id": "aiAgent", "title": "AI Agent", "icon": "$(robot)"}]}, "configuration": {"title": "AI Agent", "properties": {"aiAgent.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key for the AI Agent", "scope": "application"}, "aiAgent.openaiModel": {"type": "string", "default": "gpt-4", "enum": ["gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-3.5-turbo"], "description": "OpenAI model to use"}, "aiAgent.openaiMaxTokens": {"type": "number", "default": 4000, "minimum": 1, "maximum": 32000, "description": "Maximum tokens for AI responses"}, "aiAgent.openaiTemperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "OpenAI temperature (creativity level)"}, "aiAgent.maxContextSize": {"type": "number", "default": 100000, "description": "Maximum context size for AI processing"}, "aiAgent.allowDangerousCommands": {"type": "boolean", "default": false, "description": "Allow execution of potentially dangerous commands"}, "aiAgent.requireConfirmation": {"type": "boolean", "default": true, "description": "Require user confirmation for file changes and commands"}, "aiAgent.autoSave": {"type": "boolean", "default": true, "description": "Automatically save files after AI modifications"}, "aiAgent.debug": {"type": "boolean", "default": false, "description": "Enable debug logging"}, "aiAgent.logLevel": {"type": "string", "default": "info", "enum": ["debug", "info", "warn", "error"], "description": "Logging level"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run check-types && npm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "chokidar": "^3.5.3", "glob": "^10.3.0", "uuid": "^9.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}