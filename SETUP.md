# HEAVENLYDEMON AI Agent - Setup Guide

## Quick Start

### 1. Prerequisites
- **VS Code** 1.102.0 or higher
- **Node.js** 18.x or higher
- **OpenAI API Key** (required for AI functionality)

### 2. Installation Steps

#### Option A: Development Mode (Recommended for testing)
```bash
# 1. <PERSON><PERSON>/download the extension
cd heavenlydemon

# 2. Install dependencies
npm install

# 3. Build the extension
npm run compile

# 4. Open in VS Code
code .

# 5. Press F5 to launch Extension Development Host
```

#### Option B: Package and Install
```bash
# 1. Install vsce (VS Code Extension Manager)
npm install -g vsce

# 2. Package the extension
vsce package

# 3. Install the .vsix file
code --install-extension heavenlydemon-0.0.1.vsix
```

### 3. Configuration

#### Set OpenAI API Key
1. Open VS Code Settings (`Ctrl+,` or `Cmd+,`)
2. Search for "AI Agent"
3. Set your OpenAI API key in `aiAgent.openaiApiKey`

**Alternative: Use VS Code Secrets**
```typescript
// The extension will prompt you to enter the API key securely
// if not found in settings
```

#### Optional Settings
```json
{
  "aiAgent.model": "gpt-4",           // or "gpt-4-turbo", "gpt-4o"
  "aiAgent.maxTokens": 4000,          // Response length limit
  "aiAgent.autoSave": true            // Auto-save modified files
}
```

### 4. First Use

#### Open the AI Agent Panel
- **Method 1**: Click the robot icon (🤖) in the Activity Bar
- **Method 2**: Command Palette (`Ctrl+Shift+P`) → "AI Agent: Open AI Panel"
- **Method 3**: Use the command `heavenlydemon.openAIPanel`

#### Test with Simple Commands
```
"Hello! Can you help me understand my project?"
"What files are in my workspace?"
"Create a simple Hello World function"
```

### 5. Verify Installation

#### Check Extension is Active
1. Open Command Palette (`Ctrl+Shift+P`)
2. Look for commands starting with "AI Agent:"
   - ✅ AI Agent: Open AI Panel
   - ✅ AI Agent: Revert All Changes
   - ✅ AI Agent: Clear Agent Memory

#### Check File Structure
The extension should create:
```
.vscode/
└── ai-agent/
    ├── project.json          # Project metadata
    ├── task_queue.json       # Task management
    ├── context_store.json    # Conversation history
    ├── file_backup.json      # File change tracking
    └── logs/                 # Activity logs
        └── activity-YYYY-MM-DD.log
```

### 6. Troubleshooting

#### Extension Not Loading
```bash
# Check for compilation errors
npm run compile

# Check VS Code Developer Console
# Help → Toggle Developer Tools → Console tab
```

#### AI Not Responding
1. **Check API Key**: Ensure OpenAI API key is set correctly
2. **Check Network**: Verify internet connection
3. **Check Credits**: Ensure OpenAI account has available credits
4. **Check Logs**: Look in `.vscode/ai-agent/logs/` for errors

#### Common Error Messages

**"No workspace folder found"**
- Solution: Open a folder/workspace in VS Code before using the AI Agent

**"OpenAI API key not configured"**
- Solution: Set the API key in VS Code settings under `aiAgent.openaiApiKey`

**"Failed to index workspace"**
- Solution: Check file permissions and ensure workspace is readable

### 7. Development Mode

#### Watch Mode for Development
```bash
# Terminal 1: Watch TypeScript compilation
npm run watch

# Terminal 2: Launch Extension Development Host
# Press F5 in VS Code or use "Run Extension" from Run panel
```

#### Debug the Extension
1. Set breakpoints in TypeScript files
2. Press F5 to launch Extension Development Host
3. Use the extension in the new window
4. Debug output appears in the original VS Code window

#### Debug the Webview
1. In Extension Development Host, open AI Agent panel
2. Right-click in the panel → "Inspect Element"
3. Use Chrome DevTools to debug React components

### 8. Testing

#### Run Test Suite
```bash
npm test
```

#### Manual Testing Checklist
- [ ] Extension activates without errors
- [ ] AI Agent panel opens and displays correctly
- [ ] Can send messages and receive responses
- [ ] File operations work (create, modify, delete)
- [ ] Terminal commands execute safely
- [ ] Revert functionality works
- [ ] Memory persists across VS Code restarts

### 9. Performance Tips

#### For Large Projects
- The extension automatically ignores common directories (node_modules, .git, etc.)
- Indexing happens in the background
- File watching is debounced to prevent excessive re-indexing

#### Memory Usage
- Conversation history is stored locally
- Old logs are not automatically cleaned up
- Use "Clear Memory" if needed to reset state

### 10. Security Notes

#### Data Privacy
- All conversation history stored locally in `.vscode/ai-agent/`
- Only user messages and project context sent to OpenAI
- No telemetry or usage data collected
- API key stored securely in VS Code settings

#### Command Safety
- Dangerous commands require user confirmation
- File changes are automatically backed up
- Complete audit trail maintained in logs

---

## Need Help?

1. **Check the logs**: `.vscode/ai-agent/logs/activity-YYYY-MM-DD.log`
2. **VS Code Developer Console**: Help → Toggle Developer Tools
3. **Extension Output**: View → Output → Select "HEAVENLYDEMON AI Agent"

## Next Steps

Once setup is complete, try these example prompts:
- "Analyze my project structure and suggest improvements"
- "Add error handling to my main function"
- "Create unit tests for my utility functions"
- "Refactor this component to use TypeScript"
- "Set up a basic Express.js server"

Happy coding with your AI assistant! 🚀
