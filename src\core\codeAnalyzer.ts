import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { FileIndexer } from './fileIndexer';
import { FileChunk } from '../types';
import { CodeAnalysisResult, ProjectAnalysis } from './aiAgent';

export class CodeAnalyzer {
    private fileIndexer: FileIndexer;

    constructor(fileIndexer: FileIndexer) {
        this.fileIndexer = fileIndexer;
    }

    async analyzeProject(): Promise<ProjectAnalysis> {
        const projectIndex = this.fileIndexer.getProjectIndex();
        if (!projectIndex) {
            throw new Error('Project not indexed. Please wait for indexing to complete.');
        }

        const analysis: ProjectAnalysis = {
            architecture: await this.detectArchitecture(projectIndex),
            technologies: await this.detectTechnologies(projectIndex),
            dependencies: await this.analyzeDependencies(),
            issues: await this.detectIssues(projectIndex),
            suggestions: await this.generateSuggestions(projectIndex),
            complexity: this.calculateComplexity(projectIndex),
            maintainability: this.calculateMaintainability(projectIndex)
        };

        return analysis;
    }

    private async detectArchitecture(projectIndex: any): Promise<string> {
        const files = projectIndex.files;
        
        // Check for common architectural patterns
        if (files.some((f: string) => f.includes('src/components') && f.includes('src/pages'))) {
            return 'React/Next.js Frontend';
        }
        
        if (files.some((f: string) => f.includes('app/models') && f.includes('app/controllers'))) {
            return 'MVC (Rails/Laravel)';
        }
        
        if (files.some((f: string) => f.includes('src/main/java') && f.includes('pom.xml'))) {
            return 'Spring Boot/Maven';
        }
        
        if (files.some((f: string) => f.includes('src') && f.includes('tests') && f.endsWith('.py'))) {
            return 'Python Package';
        }
        
        if (files.some((f: string) => f.includes('src') && f.includes('Cargo.toml'))) {
            return 'Rust Crate';
        }
        
        if (files.some((f: string) => f.includes('cmd') && f.includes('go.mod'))) {
            return 'Go Module';
        }
        
        if (files.some((f: string) => f.includes('src') && f.includes('package.json'))) {
            return 'Node.js/TypeScript';
        }
        
        return 'Unknown/Custom';
    }

    private async detectTechnologies(projectIndex: any): Promise<string[]> {
        const technologies: Set<string> = new Set();
        const files = projectIndex.files;

        // Language detection
        if (files.some((f: string) => f.endsWith('.ts') || f.endsWith('.tsx'))) {
            technologies.add('TypeScript');
        }
        if (files.some((f: string) => f.endsWith('.js') || f.endsWith('.jsx'))) {
            technologies.add('JavaScript');
        }
        if (files.some((f: string) => f.endsWith('.py'))) {
            technologies.add('Python');
        }
        if (files.some((f: string) => f.endsWith('.java'))) {
            technologies.add('Java');
        }
        if (files.some((f: string) => f.endsWith('.rs'))) {
            technologies.add('Rust');
        }
        if (files.some((f: string) => f.endsWith('.go'))) {
            technologies.add('Go');
        }
        if (files.some((f: string) => f.endsWith('.cs'))) {
            technologies.add('C#');
        }

        // Framework detection
        const packageJsonPath = files.find((f: string) => f.endsWith('package.json'));
        if (packageJsonPath) {
            try {
                const content = await fs.promises.readFile(packageJsonPath, 'utf-8');
                const packageJson = JSON.parse(content);
                const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
                
                if (deps.react) technologies.add('React');
                if (deps.vue) technologies.add('Vue.js');
                if (deps.angular) technologies.add('Angular');
                if (deps.express) technologies.add('Express.js');
                if (deps.next) technologies.add('Next.js');
                if (deps.nuxt) technologies.add('Nuxt.js');
                if (deps.svelte) technologies.add('Svelte');
                if (deps.jest) technologies.add('Jest');
                if (deps.cypress) technologies.add('Cypress');
                if (deps.webpack) technologies.add('Webpack');
                if (deps.vite) technologies.add('Vite');
            } catch (error) {
                console.warn('Failed to parse package.json:', error);
            }
        }

        return Array.from(technologies);
    }

    private async analyzeDependencies(): Promise<string[]> {
        const dependencies: string[] = [];
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) return dependencies;

        const rootPath = workspaceFolder.uri.fsPath;

        // Check package.json
        const packageJsonPath = path.join(rootPath, 'package.json');
        if (fs.existsSync(packageJsonPath)) {
            try {
                const content = await fs.promises.readFile(packageJsonPath, 'utf-8');
                const packageJson = JSON.parse(content);
                const deps = Object.keys({ ...packageJson.dependencies, ...packageJson.devDependencies });
                dependencies.push(...deps);
            } catch (error) {
                console.warn('Failed to parse package.json:', error);
            }
        }

        // Check requirements.txt
        const requirementsPath = path.join(rootPath, 'requirements.txt');
        if (fs.existsSync(requirementsPath)) {
            try {
                const content = await fs.promises.readFile(requirementsPath, 'utf-8');
                const deps = content.split('\n')
                    .filter(line => line.trim() && !line.startsWith('#'))
                    .map(line => line.split('==')[0].split('>=')[0].split('<=')[0].trim());
                dependencies.push(...deps);
            } catch (error) {
                console.warn('Failed to parse requirements.txt:', error);
            }
        }

        // Check Cargo.toml
        const cargoPath = path.join(rootPath, 'Cargo.toml');
        if (fs.existsSync(cargoPath)) {
            try {
                const content = await fs.promises.readFile(cargoPath, 'utf-8');
                const deps = content.match(/\[dependencies\]([\s\S]*?)(?=\[|$)/)?.[1]
                    ?.split('\n')
                    .filter(line => line.trim() && !line.startsWith('#'))
                    .map(line => line.split('=')[0].trim()) || [];
                dependencies.push(...deps);
            } catch (error) {
                console.warn('Failed to parse Cargo.toml:', error);
            }
        }

        return dependencies;
    }

    private async detectIssues(projectIndex: any): Promise<CodeAnalysisResult[]> {
        const issues: CodeAnalysisResult[] = [];
        const chunks = projectIndex.chunks as FileChunk[];

        for (const chunk of chunks) {
            // Security issues
            issues.push(...this.detectSecurityIssues(chunk));
            
            // Performance issues
            issues.push(...this.detectPerformanceIssues(chunk));
            
            // Code quality issues
            issues.push(...this.detectQualityIssues(chunk));
            
            // Style issues
            issues.push(...this.detectStyleIssues(chunk));
        }

        return issues;
    }

    private detectSecurityIssues(chunk: FileChunk): CodeAnalysisResult[] {
        const issues: CodeAnalysisResult[] = [];
        const content = chunk.content.toLowerCase();

        // SQL Injection patterns
        if (content.includes('select * from') && content.includes('+')) {
            issues.push({
                type: 'security',
                severity: 'high',
                file: chunk.filePath,
                line: this.findLineNumber(chunk.content, 'select'),
                message: 'Potential SQL injection vulnerability detected',
                suggestion: 'Use parameterized queries or prepared statements',
                autoFixable: false
            });
        }

        // Hardcoded secrets
        const secretPatterns = [
            /password\s*=\s*["'][^"']+["']/i,
            /api[_-]?key\s*=\s*["'][^"']+["']/i,
            /secret\s*=\s*["'][^"']+["']/i,
            /token\s*=\s*["'][^"']+["']/i
        ];

        for (const pattern of secretPatterns) {
            if (pattern.test(chunk.content)) {
                issues.push({
                    type: 'security',
                    severity: 'critical',
                    file: chunk.filePath,
                    message: 'Hardcoded secret detected',
                    suggestion: 'Move secrets to environment variables or secure configuration',
                    autoFixable: false
                });
            }
        }

        return issues;
    }

    private detectPerformanceIssues(chunk: FileChunk): CodeAnalysisResult[] {
        const issues: CodeAnalysisResult[] = [];
        const content = chunk.content;

        // Nested loops
        const nestedLoopPattern = /for\s*\([^}]*for\s*\(/g;
        if (nestedLoopPattern.test(content)) {
            issues.push({
                type: 'performance',
                severity: 'medium',
                file: chunk.filePath,
                message: 'Nested loops detected - potential O(n²) complexity',
                suggestion: 'Consider optimizing with hash maps or other data structures',
                autoFixable: false
            });
        }

        // Large arrays/objects
        if (content.includes('new Array(') && /new Array\(\s*\d{4,}\s*\)/.test(content)) {
            issues.push({
                type: 'performance',
                severity: 'medium',
                file: chunk.filePath,
                message: 'Large array allocation detected',
                suggestion: 'Consider lazy loading or pagination for large datasets',
                autoFixable: false
            });
        }

        return issues;
    }

    private detectQualityIssues(chunk: FileChunk): CodeAnalysisResult[] {
        const issues: CodeAnalysisResult[] = [];
        const content = chunk.content;

        // Long functions
        const lines = content.split('\n');
        if (lines.length > 50 && chunk.type === 'function') {
            issues.push({
                type: 'issue',
                severity: 'medium',
                file: chunk.filePath,
                message: `Function is too long (${lines.length} lines)`,
                suggestion: 'Consider breaking this function into smaller, more focused functions',
                autoFixable: false
            });
        }

        // Missing error handling
        if (content.includes('JSON.parse') && !content.includes('try') && !content.includes('catch')) {
            issues.push({
                type: 'issue',
                severity: 'medium',
                file: chunk.filePath,
                message: 'JSON.parse without error handling',
                suggestion: 'Wrap JSON.parse in try-catch block',
                autoFixable: true
            });
        }

        return issues;
    }

    private detectStyleIssues(chunk: FileChunk): CodeAnalysisResult[] {
        const issues: CodeAnalysisResult[] = [];
        const content = chunk.content;

        // Missing documentation
        if (chunk.type === 'function' && !content.includes('/**') && !content.includes('//')) {
            issues.push({
                type: 'style',
                severity: 'low',
                file: chunk.filePath,
                message: 'Function lacks documentation',
                suggestion: 'Add JSDoc or inline comments to explain the function purpose',
                autoFixable: true
            });
        }

        return issues;
    }

    private async generateSuggestions(projectIndex: any): Promise<string[]> {
        const suggestions: string[] = [];
        const files = projectIndex.files;

        // Missing test files
        const hasTests = files.some((f: string) => 
            f.includes('test') || f.includes('spec') || f.includes('__tests__')
        );
        if (!hasTests) {
            suggestions.push('Add unit tests to improve code reliability');
        }

        // Missing documentation
        const hasReadme = files.some((f: string) => f.toLowerCase().includes('readme'));
        if (!hasReadme) {
            suggestions.push('Add a README.md file to document the project');
        }

        // Missing CI/CD
        const hasCi = files.some((f: string) => 
            f.includes('.github/workflows') || f.includes('.gitlab-ci') || f.includes('Jenkinsfile')
        );
        if (!hasCi) {
            suggestions.push('Set up continuous integration/deployment pipeline');
        }

        return suggestions;
    }

    private calculateComplexity(projectIndex: any): 'low' | 'medium' | 'high' {
        const totalLines = projectIndex.totalLines;
        const totalFiles = projectIndex.totalFiles;

        if (totalLines < 1000 && totalFiles < 10) return 'low';
        if (totalLines < 10000 && totalFiles < 50) return 'medium';
        return 'high';
    }

    private calculateMaintainability(projectIndex: any): number {
        let score = 100;
        const chunks = projectIndex.chunks as FileChunk[];

        // Deduct points for large functions
        const largeFunctions = chunks.filter(c => 
            c.type === 'function' && c.content.split('\n').length > 50
        ).length;
        score -= largeFunctions * 5;

        // Deduct points for missing documentation
        const undocumentedFunctions = chunks.filter(c => 
            c.type === 'function' && !c.content.includes('/**') && !c.content.includes('//')
        ).length;
        score -= undocumentedFunctions * 2;

        return Math.max(0, Math.min(100, score));
    }

    private findLineNumber(content: string, searchTerm: string): number {
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].toLowerCase().includes(searchTerm.toLowerCase())) {
                return i + 1;
            }
        }
        return 1;
    }
}
