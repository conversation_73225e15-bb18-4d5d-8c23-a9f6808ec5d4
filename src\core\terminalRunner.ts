import * as vscode from 'vscode';
import { spawn, ChildProcess } from 'child_process';
import { CommandResult, DANGEROUS_COMMANDS } from '../types';

export interface TerminalOptions {
    cwd?: string;
    timeout?: number;
    requireConfirmation?: boolean;
    allowDangerous?: boolean;
}

export class TerminalRunner {
    private activeTerminals: Map<string, vscode.Terminal> = new Map();
    private runningProcesses: Map<string, ChildProcess> = new Map();

    constructor() {}

    async executeCommand(
        command: string, 
        options: TerminalOptions = {}
    ): Promise<CommandResult> {
        const startTime = Date.now();
        
        try {
            // Security check
            if (!options.allowDangerous && this.isDangerousCommand(command)) {
                if (options.requireConfirmation !== false) {
                    const confirmed = await this.confirmDangerousCommand(command);
                    if (!confirmed) {
                        return {
                            success: false,
                            output: '',
                            error: 'Command execution cancelled by user',
                            command,
                            timestamp: new Date().toISOString()
                        };
                    }
                }
            }

            // Get workspace directory
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            const cwd = options.cwd || workspaceFolder?.uri.fsPath || process.cwd();

            // Execute command
            const result = await this.runCommand(command, cwd, options.timeout || 30000);
            
            return {
                success: result.exitCode === 0,
                output: result.stdout,
                error: result.stderr,
                exitCode: result.exitCode,
                command,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            return {
                success: false,
                output: '',
                error: String(error),
                command,
                timestamp: new Date().toISOString()
            };
        }
    }

    private async runCommand(
        command: string, 
        cwd: string, 
        timeout: number
    ): Promise<{ stdout: string; stderr: string; exitCode: number }> {
        return new Promise((resolve, reject) => {
            // Parse command and arguments
            const parts = this.parseCommand(command);
            const cmd = parts[0];
            const args = parts.slice(1);

            const childProcess = spawn(cmd, args, {
                cwd,
                shell: true,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            let stdout = '';
            let stderr = '';

            childProcess.stdout?.on('data', (data: Buffer) => {
                stdout += data.toString();
            });

            childProcess.stderr?.on('data', (data: Buffer) => {
                stderr += data.toString();
            });

            childProcess.on('close', (code) => {
                resolve({
                    stdout: stdout.trim(),
                    stderr: stderr.trim(),
                    exitCode: code || 0
                });
            });

            childProcess.on('error', (error) => {
                reject(error);
            });

            // Set timeout
            const timeoutId = setTimeout(() => {
                childProcess.kill('SIGTERM');
                reject(new Error(`Command timed out after ${timeout}ms`));
            }, timeout);

            childProcess.on('close', () => {
                clearTimeout(timeoutId);
            });
        });
    }

    async executeInTerminal(
        command: string, 
        terminalName: string = 'AI Agent',
        options: TerminalOptions = {}
    ): Promise<void> {
        try {
            // Security check
            if (!options.allowDangerous && this.isDangerousCommand(command)) {
                if (options.requireConfirmation !== false) {
                    const confirmed = await this.confirmDangerousCommand(command);
                    if (!confirmed) {
                        vscode.window.showWarningMessage('Command execution cancelled');
                        return;
                    }
                }
            }

            // Get or create terminal
            let terminal = this.activeTerminals.get(terminalName);
            if (!terminal || terminal.exitStatus) {
                terminal = vscode.window.createTerminal({
                    name: terminalName,
                    cwd: options.cwd
                });
                this.activeTerminals.set(terminalName, terminal);
            }

            // Show terminal and send command
            terminal.show();
            terminal.sendText(command);

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to execute command in terminal: ${error}`);
        }
    }

    async executeInteractive(
        command: string,
        onOutput?: (output: string) => void,
        onError?: (error: string) => void,
        options: TerminalOptions = {}
    ): Promise<ChildProcess> {
        // Security check
        if (!options.allowDangerous && this.isDangerousCommand(command)) {
            if (options.requireConfirmation !== false) {
                const confirmed = await this.confirmDangerousCommand(command);
                if (!confirmed) {
                    throw new Error('Command execution cancelled by user');
                }
            }
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const cwd = options.cwd || workspaceFolder?.uri.fsPath || process.cwd();

        const parts = this.parseCommand(command);
        const cmd = parts[0];
        const args = parts.slice(1);

        const childProcess = spawn(cmd, args, {
            cwd,
            shell: true,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        const processId = Date.now().toString();
        this.runningProcesses.set(processId, childProcess);

        if (onOutput) {
            childProcess.stdout?.on('data', (data: Buffer) => {
                onOutput(data.toString());
            });
        }

        if (onError) {
            childProcess.stderr?.on('data', (data: Buffer) => {
                onError(data.toString());
            });
        }

        childProcess.on('close', () => {
            this.runningProcesses.delete(processId);
        });

        return childProcess;
    }

    private isDangerousCommand(command: string): boolean {
        const lowerCommand = command.toLowerCase().trim();
        
        return DANGEROUS_COMMANDS.some(dangerous => {
            if (dangerous.includes(' ')) {
                // Multi-word dangerous command
                return lowerCommand.includes(dangerous.toLowerCase());
            } else {
                // Single word - check if it's the first word
                return lowerCommand.startsWith(dangerous.toLowerCase() + ' ') || 
                       lowerCommand === dangerous.toLowerCase();
            }
        });
    }

    private async confirmDangerousCommand(command: string): Promise<boolean> {
        const result = await vscode.window.showWarningMessage(
            `The command "${command}" may be dangerous. Are you sure you want to execute it?`,
            { modal: true },
            'Yes, Execute',
            'Cancel'
        );

        return result === 'Yes, Execute';
    }

    private parseCommand(command: string): string[] {
        // Simple command parsing - could be enhanced for complex cases
        const parts: string[] = [];
        let current = '';
        let inQuotes = false;
        let quoteChar = '';

        for (let i = 0; i < command.length; i++) {
            const char = command[i];

            if ((char === '"' || char === "'") && !inQuotes) {
                inQuotes = true;
                quoteChar = char;
            } else if (char === quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = '';
            } else if (char === ' ' && !inQuotes) {
                if (current.trim()) {
                    parts.push(current.trim());
                    current = '';
                }
            } else {
                current += char;
            }
        }

        if (current.trim()) {
            parts.push(current.trim());
        }

        return parts;
    }

    // Common development commands with safety checks
    async installPackage(packageName: string, packageManager: 'npm' | 'yarn' | 'pnpm' = 'npm'): Promise<CommandResult> {
        const commands = {
            npm: `npm install ${packageName}`,
            yarn: `yarn add ${packageName}`,
            pnpm: `pnpm add ${packageName}`
        };

        return await this.executeCommand(commands[packageManager], {
            requireConfirmation: true,
            timeout: 120000 // 2 minutes for package installation
        });
    }

    async runTests(testCommand?: string): Promise<CommandResult> {
        // Auto-detect test command if not provided
        if (!testCommand) {
            testCommand = await this.detectTestCommand();
        }

        return await this.executeCommand(testCommand, {
            timeout: 300000 // 5 minutes for tests
        });
    }

    private async detectTestCommand(): Promise<string> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return 'npm test';
        }

        const rootPath = workspaceFolder.uri.fsPath;
        
        // Check package.json for test script
        try {
            const packageJsonPath = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            const packageJson = JSON.parse(await vscode.workspace.fs.readFile(packageJsonPath).toString());
            
            if (packageJson.scripts?.test) {
                return 'npm test';
            }
        } catch {
            // Ignore errors
        }

        // Check for common test frameworks
        const testCommands = [
            'pytest',
            'python -m pytest',
            'cargo test',
            'go test ./...',
            'mvn test',
            'dotnet test'
        ];

        // Return first available command (simplified detection)
        return testCommands[0];
    }

    async startDevServer(command?: string): Promise<CommandResult> {
        if (!command) {
            command = await this.detectDevCommand();
        }

        // Start in terminal for interactive output
        await this.executeInTerminal(command, 'Dev Server');
        
        return {
            success: true,
            output: `Started dev server with command: ${command}`,
            command,
            timestamp: new Date().toISOString()
        };
    }

    private async detectDevCommand(): Promise<string> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return 'npm start';
        }

        // Check package.json for dev/start scripts
        try {
            const packageJsonPath = vscode.Uri.joinPath(workspaceFolder.uri, 'package.json');
            const packageJson = JSON.parse(await vscode.workspace.fs.readFile(packageJsonPath).toString());
            
            if (packageJson.scripts?.dev) {
                return 'npm run dev';
            }
            if (packageJson.scripts?.start) {
                return 'npm start';
            }
        } catch {
            // Ignore errors
        }

        return 'npm start';
    }

    killProcess(processId: string): boolean {
        const process = this.runningProcesses.get(processId);
        if (process) {
            process.kill('SIGTERM');
            this.runningProcesses.delete(processId);
            return true;
        }
        return false;
    }

    killAllProcesses(): void {
        for (const [id, process] of this.runningProcesses) {
            process.kill('SIGTERM');
        }
        this.runningProcesses.clear();
    }

    getActiveTerminals(): string[] {
        return Array.from(this.activeTerminals.keys());
    }

    closeTerminal(name: string): void {
        const terminal = this.activeTerminals.get(name);
        if (terminal) {
            terminal.dispose();
            this.activeTerminals.delete(name);
        }
    }

    dispose(): void {
        // Close all terminals
        for (const terminal of this.activeTerminals.values()) {
            terminal.dispose();
        }
        this.activeTerminals.clear();

        // Kill all processes
        this.killAllProcesses();
    }
}
