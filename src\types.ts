// Type definitions for the AI Agent extension

export interface ProjectState {
  goal: string;
  language: string;
  description: string;
  lastUpdated: string;
  version: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  priority: 'low' | 'medium' | 'high';
  dependencies?: string[];
}

export interface TaskQueue {
  tasks: Task[];
  currentTaskId?: string;
  lastUpdated: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: {
    filesModified?: string[];
    commandsExecuted?: string[];
    tokensUsed?: number;
  };
}

export interface ContextStore {
  messages: ChatMessage[];
  sessionId: string;
  lastUpdated: string;
}

export interface FileBackup {
  filePath: string;
  originalContent: string;
  modifiedContent?: string;
  timestamp: string;
  taskId?: string;
}

export interface FileBackupStore {
  backups: FileBackup[];
  lastUpdated: string;
}

export interface FileChunk {
  filePath: string;
  content: string;
  startLine: number;
  endLine: number;
  type: 'function' | 'class' | 'interface' | 'module' | 'other';
  name?: string;
}

export interface ProjectIndex {
  files: string[];
  chunks: FileChunk[];
  lastIndexed: string;
  totalFiles: number;
  totalLines: number;
}

export interface AIAgentConfig {
  openaiApiKey: string;
  model: string;
  maxTokens: number;
  autoSave: boolean;
}

export interface CommandResult {
  success: boolean;
  output: string;
  error?: string;
  exitCode?: number;
  command: string;
  timestamp: string;
}

export interface WebviewMessage {
  type: 'userInput' | 'aiResponse' | 'taskUpdate' | 'fileChange' | 'commandResult' | 'error' |
        'initialState' | 'stateUpdate' | 'processingStarted' | 'processingEnded' |
        'workspaceReady' | 'changesReverted' | 'revertChanges' | 'clearMemory' |
        'openFile' | 'requestState';
  data: any;
  timestamp: string;
}

// Constants
export const AGENT_DIRECTORY = '.vscode/ai-agent';
export const PROJECT_FILE = 'project.json';
export const TASK_QUEUE_FILE = 'task_queue.json';
export const CONTEXT_STORE_FILE = 'context_store.json';
export const FILE_BACKUP_FILE = 'file_backup.json';
export const LOGS_DIRECTORY = 'logs';

export const IGNORED_DIRECTORIES = [
  'node_modules',
  '.git',
  '__pycache__',
  '.venv',
  'venv',
  '.env',
  'dist',
  'build',
  'out',
  '.next',
  '.nuxt',
  'target',
  'bin',
  'obj'
];

export const SUPPORTED_EXTENSIONS = [
  '.ts', '.js', '.tsx', '.jsx',
  '.py', '.pyx',
  '.java', '.kt',
  '.cs', '.fs',
  '.cpp', '.c', '.h', '.hpp',
  '.rs',
  '.go',
  '.rb',
  '.php',
  '.swift',
  '.dart',
  '.json', '.yaml', '.yml',
  '.md', '.txt',
  '.html', '.css', '.scss', '.sass',
  '.sql',
  '.sh', '.bat', '.ps1'
];

export const DANGEROUS_COMMANDS = [
  'rm', 'del', 'rmdir', 'rd',
  'sudo', 'su',
  'reboot', 'shutdown',
  'format', 'fdisk',
  'dd', 'mkfs',
  'chmod 777', 'chown',
  'curl -X DELETE',
  'wget --delete-after'
];
