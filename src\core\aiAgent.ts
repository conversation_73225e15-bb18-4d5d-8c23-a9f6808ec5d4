import * as vscode from 'vscode';
import axios from 'axios';
import { MemoryManager } from './memoryManager';
import { FileIndexer } from './fileIndexer';
import { TerminalRunner } from './terminalRunner';
import { FileEditor } from './fileEditor';
import { 
    AIAgentConfig, 
    ChatMessage, 
    Task, 
    ProjectState,
    FileChunk,
    CommandResult
} from '../types';

export interface AIResponse {
    message: string;
    actions?: Array<{
        type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update';
        data: any;
    }>;
    reasoning?: string;
    nextSteps?: string[];
}

export class AIAgent {
    private config: AIAgentConfig;
    private memoryManager: MemoryManager;
    private fileIndexer: FileIndexer;
    private terminalRunner: TerminalRunner;
    private fileEditor: FileEditor;
    private isProcessing: boolean = false;

    constructor(
        config: AIAgentConfig,
        memoryManager: MemoryManager,
        fileIndexer: FileIndexer,
        terminalRunner: TerminalRunner
    ) {
        this.config = config;
        this.memoryManager = memoryManager;
        this.fileIndexer = fileIndexer;
        this.terminalRunner = terminalRunner;
        this.fileEditor = new FileEditor(memoryManager);
    }

    updateConfig(config: AIAgentConfig): void {
        this.config = config;
    }

    async processUserInput(input: string): Promise<AIResponse> {
        if (this.isProcessing) {
            throw new Error('Agent is already processing a request');
        }

        this.isProcessing = true;

        try {
            // Add user message to context
            await this.memoryManager.addMessage({
                role: 'user',
                content: input
            });

            // Build context for AI
            const context = await this.buildContext();

            // Get AI response
            const aiResponse = await this.callOpenAI(context, input);

            // Add AI response to context
            await this.memoryManager.addMessage({
                role: 'assistant',
                content: aiResponse.message,
                metadata: {
                    tokensUsed: this.estimateTokens(aiResponse.message)
                }
            });

            // Execute actions if any
            if (aiResponse.actions) {
                await this.executeActions(aiResponse.actions);
            }

            return aiResponse;

        } finally {
            this.isProcessing = false;
        }
    }

    private async buildContext(): Promise<string> {
        const projectState = await this.memoryManager.getProjectState();
        const taskQueue = await this.memoryManager.getTaskQueue();
        const contextStore = await this.memoryManager.getContextStore();
        const projectIndex = this.fileIndexer.getProjectIndex();

        // Build comprehensive context
        let context = `# AI Agent Context

## Project Information
- Language: ${projectState.language}
- Goal: ${projectState.goal || 'Not specified'}
- Description: ${projectState.description || 'Not specified'}

## Current Tasks
`;

        if (taskQueue.tasks.length > 0) {
            const currentTask = taskQueue.tasks.find(t => t.id === taskQueue.currentTaskId);
            if (currentTask) {
                context += `### Current Task: ${currentTask.title}
Description: ${currentTask.description}
Status: ${currentTask.status}
Priority: ${currentTask.priority}

`;
            }

            context += `### All Tasks:
`;
            for (const task of taskQueue.tasks) {
                context += `- [${task.status}] ${task.title}: ${task.description}\n`;
            }
        } else {
            context += 'No tasks currently defined.\n';
        }

        // Add project structure
        if (projectIndex) {
            context += `
## Project Structure
- Total Files: ${projectIndex.totalFiles}
- Total Lines: ${projectIndex.totalLines}
- Last Indexed: ${projectIndex.lastIndexed}

### Key Files:
`;
            const keyFiles = projectIndex.chunks
                .filter(chunk => chunk.type === 'class' || chunk.type === 'function')
                .slice(0, 10);
            
            for (const chunk of keyFiles) {
                context += `- ${chunk.filePath} (${chunk.type}${chunk.name ? `: ${chunk.name}` : ''})\n`;
            }
        }

        // Add recent conversation history (last 10 messages)
        const recentMessages = contextStore.messages.slice(-10);
        if (recentMessages.length > 0) {
            context += `
## Recent Conversation:
`;
            for (const msg of recentMessages) {
                context += `${msg.role}: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}\n\n`;
            }
        }

        return context;
    }

    private async callOpenAI(context: string, userInput: string): Promise<AIResponse> {
        if (!this.config.openaiApiKey) {
            throw new Error('OpenAI API key not configured. Please set it in VS Code settings.');
        }

        const systemPrompt = `You are an AI coding assistant integrated into VS Code. You help developers build, understand, and maintain software projects.

Your capabilities:
- Read and write files in the workspace
- Execute terminal commands safely
- Create and manage tasks
- Understand project structure and context
- Maintain conversation history

When responding:
1. Provide clear, helpful explanations
2. Suggest specific actions when appropriate
3. Break down complex tasks into smaller steps
4. Always consider the project context and existing code

You can perform actions by including them in your response. Available actions:
- file_write: Write content to a file
- file_create: Create a new file
- file_delete: Delete a file
- command_execute: Execute a terminal command
- task_create: Create a new task
- task_update: Update an existing task

Format actions as JSON in your response when needed.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `Context:\n${context}\n\nUser Request: ${userInput}` }
        ];

        try {
            const response = await axios.post(
                'https://api.openai.com/v1/chat/completions',
                {
                    model: this.config.model,
                    messages,
                    max_tokens: this.config.maxTokens,
                    temperature: 0.7,
                    stream: false
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.config.openaiApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 60000
                }
            );

            const aiMessage = response.data.choices[0].message.content;
            
            // Parse response for actions
            const actions = this.parseActions(aiMessage);
            
            return {
                message: aiMessage,
                actions
            };

        } catch (error) {
            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                const message = error.response?.data?.error?.message || error.message;
                
                if (status === 401) {
                    throw new Error('Invalid OpenAI API key. Please check your configuration.');
                } else if (status === 429) {
                    throw new Error('OpenAI API rate limit exceeded. Please try again later.');
                } else {
                    throw new Error(`OpenAI API error: ${message}`);
                }
            }
            throw new Error(`Failed to call OpenAI API: ${error}`);
        }
    }

    private parseActions(message: string): Array<{ type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update'; data: any }> {
        const actions: Array<{ type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update'; data: any }> = [];
        
        // Look for JSON blocks in the message
        const jsonRegex = /```json\s*([\s\S]*?)\s*```/g;
        let match;
        
        while ((match = jsonRegex.exec(message)) !== null) {
            try {
                const jsonData = JSON.parse(match[1]);
                if (jsonData.action && jsonData.data) {
                    const actionType = jsonData.action as 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update';
                    if (['file_write', 'file_create', 'file_delete', 'command_execute', 'task_create', 'task_update'].includes(actionType)) {
                        actions.push({
                            type: actionType,
                            data: jsonData.data
                        });
                    }
                }
            } catch (error) {
                console.warn('Failed to parse JSON action:', error);
            }
        }

        return actions;
    }

    private async executeActions(actions: Array<{ type: string; data: any }>): Promise<void> {
        for (const action of actions) {
            try {
                await this.executeAction(action.type, action.data);
            } catch (error) {
                console.error(`Failed to execute action ${action.type}:`, error);
                vscode.window.showErrorMessage(`Failed to execute action: ${error}`);
            }
        }
    }

    private async executeAction(type: string, data: any): Promise<void> {
        switch (type) {
            case 'file_write':
                await this.fileEditor.writeFile(data.path, data.content, true, data.taskId);
                break;

            case 'file_create':
                await this.fileEditor.createFile(data.path, data.content, data.taskId);
                break;

            case 'file_delete':
                await this.fileEditor.deleteFile(data.path, true, data.taskId);
                break;

            case 'command_execute':
                const result = await this.terminalRunner.executeCommand(data.command, {
                    requireConfirmation: data.requireConfirmation !== false
                });
                
                // Add command result to context
                await this.memoryManager.addMessage({
                    role: 'system',
                    content: `Command executed: ${data.command}\nOutput: ${result.output}\nError: ${result.error || 'None'}`,
                    metadata: {
                        commandsExecuted: [data.command]
                    }
                });
                break;

            case 'task_create':
                await this.memoryManager.addTask({
                    title: data.title,
                    description: data.description,
                    status: data.status || 'pending',
                    priority: data.priority || 'medium',
                    dependencies: data.dependencies
                });
                break;

            case 'task_update':
                await this.memoryManager.updateTask(data.taskId, {
                    status: data.status,
                    title: data.title,
                    description: data.description,
                    priority: data.priority
                });
                break;

            default:
                console.warn(`Unknown action type: ${type}`);
        }
    }

    async searchCode(query: string): Promise<FileChunk[]> {
        return this.fileIndexer.searchFiles(query);
    }

    async getProjectSummary(): Promise<string> {
        const projectState = await this.memoryManager.getProjectState();
        const projectIndex = this.fileIndexer.getProjectIndex();
        const taskQueue = await this.memoryManager.getTaskQueue();

        let summary = `# Project Summary

**Language:** ${projectState.language}
**Goal:** ${projectState.goal || 'Not specified'}

`;

        if (projectIndex) {
            summary += `**Files:** ${projectIndex.totalFiles} files, ${projectIndex.totalLines} lines of code
`;
        }

        if (taskQueue.tasks.length > 0) {
            const completed = taskQueue.tasks.filter(t => t.status === 'completed').length;
            const inProgress = taskQueue.tasks.filter(t => t.status === 'in_progress').length;
            const pending = taskQueue.tasks.filter(t => t.status === 'pending').length;

            summary += `**Tasks:** ${completed} completed, ${inProgress} in progress, ${pending} pending
`;
        }

        return summary;
    }

    async revertAllChanges(): Promise<void> {
        const result = await this.fileEditor.revertAllFiles();
        
        await this.memoryManager.logActivity('All changes reverted', {
            reverted: result.reverted.length,
            failed: result.failed.length
        });

        if (result.failed.length > 0) {
            throw new Error(`Failed to revert ${result.failed.length} files: ${result.failed.join(', ')}`);
        }
    }

    private estimateTokens(text: string): number {
        // Rough estimation: 1 token ≈ 4 characters
        return Math.ceil(text.length / 4);
    }

    isProcessingRequest(): boolean {
        return this.isProcessing;
    }

    dispose(): void {
        this.terminalRunner.dispose();
        this.fileIndexer.dispose();
    }
}
