import * as vscode from 'vscode';
import { MemoryManager } from './memoryManager';
import { FileIndexer } from './fileIndexer';
import { TerminalRunner } from './terminalRunner';
import { FileEditor } from './fileEditor';
import { ConfigManager, AIAgentConfiguration } from './configManager';
import { OpenAIClient, OpenAIMessage } from './openaiClient';
import {
    ChatMessage,
    Task,
    ProjectState,
    FileChunk,
    CommandResult
} from '../types';

export interface AIResponse {
    message: string;
    actions?: Array<{
        type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update';
        data: any;
    }>;
    reasoning?: string;
    nextSteps?: string[];
    confidence?: number;
    tokensUsed?: number;
    analysisResults?: CodeAnalysisResult[];
}

export interface CodeAnalysisResult {
    type: 'issue' | 'suggestion' | 'security' | 'performance' | 'style';
    severity: 'low' | 'medium' | 'high' | 'critical';
    file: string;
    line?: number;
    message: string;
    suggestion?: string;
    autoFixable?: boolean;
}

export interface ProjectAnalysis {
    architecture: string;
    technologies: string[];
    dependencies: string[];
    issues: CodeAnalysisResult[];
    suggestions: string[];
    complexity: 'low' | 'medium' | 'high';
    maintainability: number; // 0-100
    testCoverage?: number;
}

export class AIAgent {
    private config: AIAgentConfiguration;
    private configManager: ConfigManager;
    private openaiClient: OpenAIClient;
    private memoryManager: MemoryManager;
    private fileIndexer: FileIndexer;
    private terminalRunner: TerminalRunner;
    private fileEditor: FileEditor;
    private isProcessing: boolean = false;
    private conversationContext: OpenAIMessage[] = [];

    constructor(
        configManager: ConfigManager,
        memoryManager: MemoryManager,
        fileIndexer: FileIndexer,
        terminalRunner: TerminalRunner
    ) {
        this.configManager = configManager;
        this.config = configManager.getConfig();
        this.openaiClient = new OpenAIClient(this.config);
        this.memoryManager = memoryManager;
        this.fileIndexer = fileIndexer;
        this.terminalRunner = terminalRunner;
        this.fileEditor = new FileEditor(memoryManager);

        // Initialize conversation context
        this.initializeConversationContext();
    }

    private async initializeConversationContext(): Promise<void> {
        // Load previous conversation context
        const contextStore = await this.memoryManager.getContextStore();
        const projectState = await this.memoryManager.getProjectState();

        // Build system prompt with project context
        const systemPrompt = this.buildSystemPrompt(projectState);

        this.conversationContext = [
            { role: 'system', content: systemPrompt }
        ];

        // Add recent conversation history (last 10 messages)
        const recentMessages = contextStore.messages.slice(-10);
        for (const msg of recentMessages) {
            this.conversationContext.push({
                role: msg.role as 'user' | 'assistant',
                content: msg.content
            });
        }
    }

    private buildSystemPrompt(projectState: ProjectState): string {
        return `You are HEAVENLYDEMON AI Agent, an expert software development assistant integrated into VS Code. You are a commercial-grade coding agent capable of understanding, analyzing, and building complete software projects.

## Your Capabilities:
- **Code Analysis**: Deep understanding of code structure, patterns, and architecture
- **Issue Detection**: Identify bugs, security vulnerabilities, performance issues, and code smells
- **Code Generation**: Write production-quality code in any language
- **Project Management**: Break down complex tasks into manageable steps
- **Testing**: Create comprehensive test suites and debugging strategies
- **Refactoring**: Improve code quality, maintainability, and performance
- **Documentation**: Generate clear, comprehensive documentation
- **Architecture**: Design and implement scalable software architectures

## Current Project Context:
- **Language**: ${projectState.language}
- **Goal**: ${projectState.goal || 'Not specified'}
- **Description**: ${projectState.description || 'Not specified'}

## Your Approach:
1. **Analyze First**: Always understand the existing codebase before making changes
2. **Plan Thoroughly**: Break complex tasks into clear, actionable steps
3. **Code Quality**: Write clean, maintainable, well-documented code
4. **Safety First**: Always backup files before modifications
5. **Test Everything**: Suggest and implement comprehensive testing
6. **Explain Clearly**: Provide detailed explanations of your reasoning

## Available Actions:
You can perform actions by including JSON blocks in your responses:

\`\`\`json
{
  "action": "file_write",
  "data": {
    "path": "src/example.js",
    "content": "// Your code here",
    "taskId": "optional-task-id"
  }
}
\`\`\`

Available actions: file_write, file_create, file_delete, command_execute, task_create, task_update

## Guidelines:
- Always explain your reasoning before taking actions
- Suggest multiple approaches when appropriate
- Consider security, performance, and maintainability
- Ask for clarification when requirements are unclear
- Provide step-by-step guidance for complex tasks
- Use industry best practices and modern development patterns

You are now ready to assist with any development task. How can I help you build something amazing?`;
    }

    updateConfig(config: AIAgentConfiguration): void {
        this.config = config;
        this.openaiClient.updateConfig(config);
    }

    async processUserInput(input: string): Promise<AIResponse> {
        if (this.isProcessing) {
            throw new Error('Agent is already processing a request');
        }

        this.isProcessing = true;

        try {
            // Add user message to context
            await this.memoryManager.addMessage({
                role: 'user',
                content: input
            });

            // Build context for AI
            const context = await this.buildContext();

            // Get AI response
            const aiResponse = await this.callOpenAI(context, input);

            // Add AI response to context
            await this.memoryManager.addMessage({
                role: 'assistant',
                content: aiResponse.message,
                metadata: {
                    tokensUsed: this.estimateTokens(aiResponse.message)
                }
            });

            // Execute actions if any
            if (aiResponse.actions) {
                await this.executeActions(aiResponse.actions);
            }

            return aiResponse;

        } finally {
            this.isProcessing = false;
        }
    }

    private async buildContext(): Promise<string> {
        const projectState = await this.memoryManager.getProjectState();
        const taskQueue = await this.memoryManager.getTaskQueue();
        const contextStore = await this.memoryManager.getContextStore();
        const projectIndex = this.fileIndexer.getProjectIndex();

        // Build comprehensive context
        let context = `# AI Agent Context

## Project Information
- Language: ${projectState.language}
- Goal: ${projectState.goal || 'Not specified'}
- Description: ${projectState.description || 'Not specified'}

## Current Tasks
`;

        if (taskQueue.tasks.length > 0) {
            const currentTask = taskQueue.tasks.find(t => t.id === taskQueue.currentTaskId);
            if (currentTask) {
                context += `### Current Task: ${currentTask.title}
Description: ${currentTask.description}
Status: ${currentTask.status}
Priority: ${currentTask.priority}

`;
            }

            context += `### All Tasks:
`;
            for (const task of taskQueue.tasks) {
                context += `- [${task.status}] ${task.title}: ${task.description}\n`;
            }
        } else {
            context += 'No tasks currently defined.\n';
        }

        // Add project structure
        if (projectIndex) {
            context += `
## Project Structure
- Total Files: ${projectIndex.totalFiles}
- Total Lines: ${projectIndex.totalLines}
- Last Indexed: ${projectIndex.lastIndexed}

### Key Files:
`;
            const keyFiles = projectIndex.chunks
                .filter(chunk => chunk.type === 'class' || chunk.type === 'function')
                .slice(0, 10);
            
            for (const chunk of keyFiles) {
                context += `- ${chunk.filePath} (${chunk.type}${chunk.name ? `: ${chunk.name}` : ''})\n`;
            }
        }

        // Add recent conversation history (last 10 messages)
        const recentMessages = contextStore.messages.slice(-10);
        if (recentMessages.length > 0) {
            context += `
## Recent Conversation:
`;
            for (const msg of recentMessages) {
                context += `${msg.role}: ${msg.content.substring(0, 200)}${msg.content.length > 200 ? '...' : ''}\n\n`;
            }
        }

        return context;
    }

    private async callOpenAI(context: string, userInput: string): Promise<AIResponse> {
        // Validate API key
        const validation = await this.openaiClient.validateApiKey();
        if (!validation.valid) {
            // Try to prompt for API key
            const apiKey = await this.configManager.promptForApiKey();
            if (!apiKey) {
                throw new Error(validation.error || 'OpenAI API key is required');
            }
            // Update client with new key
            this.config = this.configManager.getConfig();
            this.openaiClient.updateConfig(this.config);
        }

        // Build enhanced context with code analysis
        const enhancedContext = await this.buildEnhancedContext(context, userInput);

        // Prepare messages for OpenAI
        const messages: OpenAIMessage[] = [
            ...this.conversationContext,
            { role: 'user', content: `${enhancedContext}\n\nUser Request: ${userInput}` }
        ];

        try {
            const response = await this.openaiClient.createChatCompletion(messages, {
                temperature: this.config.openaiTemperature,
                maxTokens: this.config.openaiMaxTokens
            });

            const aiMessage = response.choices[0].message.content;
            const tokensUsed = response.usage.total_tokens;

            // Parse response for actions and analysis
            const actions = this.parseActions(aiMessage);
            const confidence = this.calculateConfidence(response);

            // Update conversation context
            this.conversationContext.push(
                { role: 'user', content: userInput },
                { role: 'assistant', content: aiMessage }
            );

            // Keep context manageable
            if (this.conversationContext.length > 20) {
                this.conversationContext = [
                    this.conversationContext[0], // Keep system prompt
                    ...this.conversationContext.slice(-19) // Keep last 19 messages
                ];
            }

            return {
                message: aiMessage,
                actions,
                confidence,
                tokensUsed
            };

        } catch (error) {
            throw new Error(`AI processing failed: ${error}`);
        }
    }

    private async buildEnhancedContext(baseContext: string, userInput: string): Promise<string> {
        let enhancedContext = baseContext;

        // Add relevant code snippets based on user input
        const relevantCode = await this.findRelevantCode(userInput);
        if (relevantCode.length > 0) {
            enhancedContext += '\n\n## Relevant Code:\n';
            for (const chunk of relevantCode.slice(0, 5)) { // Limit to 5 most relevant
                enhancedContext += `\n### ${chunk.filePath}${chunk.name ? ` - ${chunk.name}` : ''}\n`;
                enhancedContext += '```' + this.getLanguageFromPath(chunk.filePath) + '\n';
                enhancedContext += chunk.content.substring(0, 1000); // Limit content size
                enhancedContext += '\n```\n';
            }
        }

        // Add project analysis if requested
        if (this.isAnalysisRequest(userInput)) {
            try {
                const analysis = await this.analyzeProject();
                enhancedContext += '\n\n## Project Analysis:\n';
                enhancedContext += `Architecture: ${analysis.architecture}\n`;
                enhancedContext += `Technologies: ${analysis.technologies.join(', ')}\n`;
                enhancedContext += `Complexity: ${analysis.complexity}\n`;
                enhancedContext += `Maintainability: ${analysis.maintainability}/100\n`;

                if (analysis.issues.length > 0) {
                    enhancedContext += `\nIssues Found: ${analysis.issues.length}\n`;
                    // Include top 3 most severe issues
                    const topIssues = analysis.issues
                        .sort((a, b) => this.getSeverityWeight(b.severity) - this.getSeverityWeight(a.severity))
                        .slice(0, 3);
                    for (const issue of topIssues) {
                        enhancedContext += `- ${issue.severity.toUpperCase()}: ${issue.message} (${issue.file})\n`;
                    }
                }
            } catch (error) {
                console.warn('Failed to add project analysis to context:', error);
            }
        }

        return enhancedContext;
    }

    private async findRelevantCode(userInput: string): Promise<FileChunk[]> {
        // Extract keywords from user input
        const keywords = this.extractKeywords(userInput);

        // Search for relevant code chunks
        let relevantChunks: FileChunk[] = [];
        for (const keyword of keywords) {
            const chunks = this.fileIndexer.searchFiles(keyword);
            relevantChunks.push(...chunks);
        }

        // Remove duplicates and sort by relevance
        const uniqueChunks = Array.from(new Map(
            relevantChunks.map(chunk => [`${chunk.filePath}:${chunk.startLine}`, chunk])
        ).values());

        return uniqueChunks.slice(0, 10); // Return top 10 most relevant
    }

    private extractKeywords(text: string): string[] {
        // Simple keyword extraction - could be enhanced with NLP
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        // Remove common words
        const stopWords = new Set(['this', 'that', 'with', 'from', 'they', 'been', 'have', 'were', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'would', 'there', 'could', 'other']);

        return words.filter(word => !stopWords.has(word));
    }

    private isAnalysisRequest(userInput: string): boolean {
        const analysisKeywords = ['analyze', 'analysis', 'review', 'audit', 'check', 'issues', 'problems', 'quality', 'structure', 'architecture'];
        const lowerInput = userInput.toLowerCase();
        return analysisKeywords.some(keyword => lowerInput.includes(keyword));
    }

    private calculateConfidence(response: any): number {
        // Simple confidence calculation based on response characteristics
        let confidence = 0.8; // Base confidence

        // Adjust based on response length (longer responses might be more detailed)
        const messageLength = response.choices[0].message.content.length;
        if (messageLength > 500) confidence += 0.1;
        if (messageLength < 100) confidence -= 0.2;

        // Adjust based on finish reason
        if (response.choices[0].finish_reason === 'stop') confidence += 0.1;
        if (response.choices[0].finish_reason === 'length') confidence -= 0.1;

        return Math.max(0.1, Math.min(1.0, confidence));
    }

    private getSeverityWeight(severity: string): number {
        const weights = { critical: 4, high: 3, medium: 2, low: 1 };
        return weights[severity as keyof typeof weights] || 0;
    }

    private getLanguageFromPath(filePath: string): string {
        const ext = filePath.split('.').pop()?.toLowerCase();
        const langMap: Record<string, string> = {
            'js': 'javascript', 'jsx': 'javascript',
            'ts': 'typescript', 'tsx': 'typescript',
            'py': 'python', 'java': 'java',
            'cs': 'csharp', 'rs': 'rust',
            'go': 'go', 'cpp': 'cpp', 'c': 'c'
        };
        return langMap[ext || ''] || '';
    }

    private parseActions(message: string): Array<{ type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update'; data: any }> {
        const actions: Array<{ type: 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update'; data: any }> = [];
        
        // Look for JSON blocks in the message
        const jsonRegex = /```json\s*([\s\S]*?)\s*```/g;
        let match;
        
        while ((match = jsonRegex.exec(message)) !== null) {
            try {
                const jsonData = JSON.parse(match[1]);
                if (jsonData.action && jsonData.data) {
                    const actionType = jsonData.action as 'file_write' | 'file_create' | 'file_delete' | 'command_execute' | 'task_create' | 'task_update';
                    if (['file_write', 'file_create', 'file_delete', 'command_execute', 'task_create', 'task_update'].includes(actionType)) {
                        actions.push({
                            type: actionType,
                            data: jsonData.data
                        });
                    }
                }
            } catch (error) {
                console.warn('Failed to parse JSON action:', error);
            }
        }

        return actions;
    }

    private async executeActions(actions: Array<{ type: string; data: any }>): Promise<void> {
        for (const action of actions) {
            try {
                await this.executeAction(action.type, action.data);
            } catch (error) {
                console.error(`Failed to execute action ${action.type}:`, error);
                vscode.window.showErrorMessage(`Failed to execute action: ${error}`);
            }
        }
    }

    private async executeAction(type: string, data: any): Promise<void> {
        switch (type) {
            case 'file_write':
                await this.fileEditor.writeFile(data.path, data.content, true, data.taskId);
                break;

            case 'file_create':
                await this.fileEditor.createFile(data.path, data.content, data.taskId);
                break;

            case 'file_delete':
                await this.fileEditor.deleteFile(data.path, true, data.taskId);
                break;

            case 'command_execute':
                const result = await this.terminalRunner.executeCommand(data.command, {
                    requireConfirmation: data.requireConfirmation !== false
                });
                
                // Add command result to context
                await this.memoryManager.addMessage({
                    role: 'system',
                    content: `Command executed: ${data.command}\nOutput: ${result.output}\nError: ${result.error || 'None'}`,
                    metadata: {
                        commandsExecuted: [data.command]
                    }
                });
                break;

            case 'task_create':
                await this.memoryManager.addTask({
                    title: data.title,
                    description: data.description,
                    status: data.status || 'pending',
                    priority: data.priority || 'medium',
                    dependencies: data.dependencies
                });
                break;

            case 'task_update':
                await this.memoryManager.updateTask(data.taskId, {
                    status: data.status,
                    title: data.title,
                    description: data.description,
                    priority: data.priority
                });
                break;

            default:
                console.warn(`Unknown action type: ${type}`);
        }
    }

    async searchCode(query: string): Promise<FileChunk[]> {
        return this.fileIndexer.searchFiles(query);
    }

    async analyzeProject(): Promise<ProjectAnalysis> {
        const { CodeAnalyzer } = await import('./codeAnalyzer.js');
        const analyzer = new CodeAnalyzer(this.fileIndexer);
        return await analyzer.analyzeProject();
    }

    async getProjectSummary(): Promise<string> {
        const projectState = await this.memoryManager.getProjectState();
        const projectIndex = this.fileIndexer.getProjectIndex();
        const taskQueue = await this.memoryManager.getTaskQueue();

        let summary = `# Project Summary

**Language:** ${projectState.language}
**Goal:** ${projectState.goal || 'Not specified'}

`;

        if (projectIndex) {
            summary += `**Files:** ${projectIndex.totalFiles} files, ${projectIndex.totalLines} lines of code
`;
        }

        if (taskQueue.tasks.length > 0) {
            const completed = taskQueue.tasks.filter(t => t.status === 'completed').length;
            const inProgress = taskQueue.tasks.filter(t => t.status === 'in_progress').length;
            const pending = taskQueue.tasks.filter(t => t.status === 'pending').length;

            summary += `**Tasks:** ${completed} completed, ${inProgress} in progress, ${pending} pending
`;
        }

        // Add analysis summary
        try {
            const analysis = await this.analyzeProject();
            summary += `
**Architecture:** ${analysis.architecture}
**Technologies:** ${analysis.technologies.join(', ')}
**Complexity:** ${analysis.complexity}
**Maintainability:** ${analysis.maintainability}/100
**Issues Found:** ${analysis.issues.length}
`;
        } catch (error) {
            console.warn('Failed to add analysis to summary:', error);
        }

        return summary;
    }

    async generateProjectPlan(goal: string): Promise<string[]> {
        const context = await this.buildContext();
        const prompt = `Based on the current project state and the goal "${goal}", create a detailed step-by-step plan to achieve this goal. Consider the existing codebase, architecture, and best practices.`;

        try {
            const response = await this.callOpenAI(context, prompt);

            // Extract steps from the response
            const steps = this.extractStepsFromResponse(response.message);

            // Create tasks for each step
            for (let i = 0; i < steps.length; i++) {
                await this.memoryManager.addTask({
                    title: `Step ${i + 1}: ${steps[i].split('.')[0]}`,
                    description: steps[i],
                    status: 'pending',
                    priority: i < 3 ? 'high' : 'medium'
                });
            }

            return steps;
        } catch (error) {
            throw new Error(`Failed to generate project plan: ${error}`);
        }
    }

    private extractStepsFromResponse(response: string): string[] {
        // Extract numbered or bulleted steps from the response
        const lines = response.split('\n');
        const steps: string[] = [];

        for (const line of lines) {
            const trimmed = line.trim();
            // Match numbered steps (1., 2., etc.) or bullet points (-, *, etc.)
            if (/^\d+\./.test(trimmed) || /^[-*•]/.test(trimmed)) {
                steps.push(trimmed.replace(/^\d+\.\s*/, '').replace(/^[-*•]\s*/, ''));
            }
        }

        return steps.length > 0 ? steps : [response]; // Fallback to full response if no steps found
    }

    async fixIssue(issueDescription: string): Promise<AIResponse> {
        // Analyze the project to find relevant issues
        const analysis = await this.analyzeProject();
        const relevantIssues = analysis.issues.filter(issue =>
            issue.message.toLowerCase().includes(issueDescription.toLowerCase()) ||
            issueDescription.toLowerCase().includes(issue.type)
        );

        let context = await this.buildContext();

        if (relevantIssues.length > 0) {
            context += '\n\n## Identified Issues:\n';
            for (const issue of relevantIssues) {
                context += `- ${issue.severity.toUpperCase()}: ${issue.message} in ${issue.file}\n`;
                if (issue.suggestion) {
                    context += `  Suggestion: ${issue.suggestion}\n`;
                }
            }
        }

        const prompt = `Please help fix this issue: ${issueDescription}. Analyze the code, identify the root cause, and provide a complete solution with code changes.`;

        return await this.callOpenAI(context, prompt);
    }

    async optimizePerformance(): Promise<AIResponse> {
        const analysis = await this.analyzeProject();
        const performanceIssues = analysis.issues.filter(issue => issue.type === 'performance');

        let context = await this.buildContext();
        context += '\n\n## Performance Issues Found:\n';

        for (const issue of performanceIssues) {
            context += `- ${issue.message} in ${issue.file}\n`;
        }

        const prompt = 'Analyze the codebase for performance optimization opportunities. Focus on algorithmic improvements, memory usage, and execution speed. Provide specific code changes to improve performance.';

        return await this.callOpenAI(context, prompt);
    }

    async generateTests(filePath?: string): Promise<AIResponse> {
        let context = await this.buildContext();

        if (filePath) {
            // Get specific file content
            const fileContent = await this.fileEditor.readFile(filePath);
            context += `\n\n## File to Test: ${filePath}\n\`\`\`\n${fileContent}\n\`\`\`\n`;
        }

        const prompt = filePath
            ? `Generate comprehensive unit tests for the file ${filePath}. Include edge cases, error scenarios, and integration tests where appropriate.`
            : 'Analyze the project and generate a comprehensive test suite. Include unit tests, integration tests, and end-to-end tests as appropriate for the project type.';

        return await this.callOpenAI(context, prompt);
    }

    async revertAllChanges(): Promise<void> {
        const result = await this.fileEditor.revertAllFiles();
        
        await this.memoryManager.logActivity('All changes reverted', {
            reverted: result.reverted.length,
            failed: result.failed.length
        });

        if (result.failed.length > 0) {
            throw new Error(`Failed to revert ${result.failed.length} files: ${result.failed.join(', ')}`);
        }
    }

    private estimateTokens(text: string): number {
        // Rough estimation: 1 token ≈ 4 characters
        return Math.ceil(text.length / 4);
    }

    isProcessingRequest(): boolean {
        return this.isProcessing;
    }

    dispose(): void {
        this.terminalRunner.dispose();
        this.fileIndexer.dispose();
    }
}
