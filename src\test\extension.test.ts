import * as assert from 'assert';
import * as vscode from 'vscode';
import { MemoryManager } from '../core/memoryManager';
import { FileIndexer } from '../core/fileIndexer';
import { TerminalRunner } from '../core/terminalRunner';

suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');

    test('Extension should be present', () => {
        assert.ok(vscode.extensions.getExtension('heavenlydemon.heavenlydemon'));
    });

    test('Extension should activate', async () => {
        const extension = vscode.extensions.getExtension('heavenlydemon.heavenlydemon');
        if (extension) {
            await extension.activate();
            assert.ok(extension.isActive);
        }
    });

    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        assert.ok(commands.includes('heavenlydemon.openAIPanel'));
        assert.ok(commands.includes('heavenlydemon.revertChanges'));
        assert.ok(commands.includes('heavenlydemon.clearMemory'));
    });
});

// Note: MemoryManager tests would require a proper workspace setup
// For now, we'll focus on basic component tests

suite('FileIndexer Tests', () => {
    let fileIndexer: FileIndexer;

    setup(() => {
        fileIndexer = new FileIndexer();
    });

    test('Should create FileIndexer instance', () => {
        assert.ok(fileIndexer);
    });

    test('Should return null for project index initially', () => {
        const index = fileIndexer.getProjectIndex();
        assert.strictEqual(index, null);
    });

    test('Should return empty array for search with no index', () => {
        const results = fileIndexer.searchFiles('test');
        assert.strictEqual(results.length, 0);
    });
});

suite('TerminalRunner Tests', () => {
    let terminalRunner: TerminalRunner;

    setup(() => {
        terminalRunner = new TerminalRunner();
    });

    test('Should create TerminalRunner instance', () => {
        assert.ok(terminalRunner);
    });

    test('Should return empty array for active terminals initially', () => {
        const terminals = terminalRunner.getActiveTerminals();
        assert.strictEqual(terminals.length, 0);
    });

    test('Should handle safe commands', async () => {
        const result = await terminalRunner.executeCommand('echo "test"', {
            timeout: 5000,
            allowDangerous: false
        });

        assert.ok(result);
        assert.strictEqual(typeof result.success, 'boolean');
        assert.strictEqual(typeof result.output, 'string');
        assert.strictEqual(typeof result.command, 'string');
        assert.strictEqual(result.command, 'echo "test"');
    });
});
