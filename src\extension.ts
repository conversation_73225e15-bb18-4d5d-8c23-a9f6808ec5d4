import * as vscode from 'vscode';
import { MemoryManager } from './core/memoryManager';
import { AIAgent } from './core/aiAgent';
import { FileIndexer } from './core/fileIndexer';
import { TerminalRunner } from './core/terminalRunner';
import { ConfigManager } from './core/configManager';
import { PanelProvider } from './ui/panelProvider';

let aiAgent: AIAgent;
let panelProvider: PanelProvider;
let configManager: ConfigManager;
let memoryManager: MemoryManager;
let fileIndexer: FileIndexer;
let terminalRunner: TerminalRunner;

export async function activate(context: vscode.ExtensionContext) {
    console.log('HEAVENLYDEMON AI Agent extension is now active!');

    try {
        // Initialize configuration manager
        configManager = ConfigManager.getInstance(context);

        // Validate configuration
        const validation = await configManager.validateConfiguration();
        if (!validation.valid) {
            for (const error of validation.errors) {
                vscode.window.showWarningMessage(`AI Agent: ${error}`);
            }

            // If no API key, prompt for it
            if (!configManager.getConfig().openaiApiKey) {
                await configManager.promptForApiKey();
            }
        }

        // Initialize core components
        memoryManager = new MemoryManager(context);
        fileIndexer = new FileIndexer();
        terminalRunner = new TerminalRunner();

        // Initialize AI Agent with config manager
        aiAgent = new AIAgent(configManager, memoryManager, fileIndexer, terminalRunner);

    // Initialize UI Panel Provider
    panelProvider = new PanelProvider(context, aiAgent);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(PanelProvider.viewType, panelProvider)
    );

    // Register commands
    registerCommands(context);

    // Initialize workspace if it exists
    if (vscode.workspace.workspaceFolders) {
        await initializeWorkspace();
    }

    // Watch for workspace changes
    vscode.workspace.onDidChangeWorkspaceFolders(async () => {
        if (vscode.workspace.workspaceFolders) {
            await initializeWorkspace();
        }
    });

        // Watch for configuration changes
        const configWatcher = configManager.onConfigurationChanged(() => {
            const newConfig = configManager.getConfig();
            aiAgent.updateConfig(newConfig);
            vscode.window.showInformationMessage('AI Agent configuration updated');
        });
        context.subscriptions.push(configWatcher);

        console.log('HEAVENLYDEMON AI Agent extension initialization complete!');

        // Show welcome message on first activation
        const isFirstActivation = context.globalState.get('aiAgent.firstActivation', true);
        if (isFirstActivation) {
            await context.globalState.update('aiAgent.firstActivation', false);
            vscode.window.showInformationMessage(
                'Welcome to HEAVENLYDEMON AI Agent! Click the robot icon in the Activity Bar to get started.',
                'Open AI Panel'
            ).then(selection => {
                if (selection === 'Open AI Panel') {
                    vscode.commands.executeCommand('heavenlydemon.openAIPanel');
                }
            });
        }

    } catch (error) {
        console.error('Failed to initialize AI Agent extension:', error);
        vscode.window.showErrorMessage(`AI Agent initialization failed: ${error}`);
    }
}

function registerCommands(context: vscode.ExtensionContext) {
    // Open AI Panel command
    const openPanelCommand = vscode.commands.registerCommand('heavenlydemon.openAIPanel', () => {
        panelProvider.createOrShow();
    });

    // Revert changes command
    const revertChangesCommand = vscode.commands.registerCommand('heavenlydemon.revertChanges', async () => {
        const result = await vscode.window.showWarningMessage(
            'Are you sure you want to revert all changes made by the AI Agent?',
            { modal: true },
            'Yes, Revert All',
            'Cancel'
        );

        if (result === 'Yes, Revert All') {
            try {
                await aiAgent.revertAllChanges();
                vscode.window.showInformationMessage('All changes have been reverted successfully.');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to revert changes: ${error}`);
            }
        }
    });

    // Clear memory command
    const clearMemoryCommand = vscode.commands.registerCommand('heavenlydemon.clearMemory', async () => {
        const result = await vscode.window.showWarningMessage(
            'Are you sure you want to clear all AI Agent memory? This will delete conversation history and task progress.',
            { modal: true },
            'Yes, Clear Memory',
            'Cancel'
        );

        if (result === 'Yes, Clear Memory') {
            try {
                await memoryManager.clearAllMemory();
                vscode.window.showInformationMessage('AI Agent memory has been cleared.');
                // Refresh the panel
                panelProvider.refresh();
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to clear memory: ${error}`);
            }
        }
    });

    context.subscriptions.push(openPanelCommand, revertChangesCommand, clearMemoryCommand);
}

// Configuration is now handled by ConfigManager

async function initializeWorkspace() {
    try {
        // Initialize memory for the workspace
        await memoryManager.initializeWorkspace();

        // Index the workspace files
        await fileIndexer.indexWorkspace();

        // Notify the panel that workspace is ready
        panelProvider.notifyWorkspaceReady();

        console.log('Workspace initialized successfully');
    } catch (error) {
        console.error('Failed to initialize workspace:', error);
        vscode.window.showErrorMessage(`Failed to initialize AI Agent workspace: ${error}`);
    }
}

export function deactivate() {
    // Clean up resources
    if (panelProvider) {
        panelProvider.dispose();
    }
    console.log('AI Agent extension deactivated');
}
